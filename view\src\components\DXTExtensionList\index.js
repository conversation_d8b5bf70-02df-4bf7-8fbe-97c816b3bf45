import React, { useState, useEffect, useCallback } from 'react';
import { 
    List, 
    Card, 
    Button, 
    Switch, 
    Tag, 
    Typography, 
    Space, 
    Popconfirm, 
    message, 
    Empty, 
    Tooltip,
    Modal,
    Descriptions,
    Alert
} from 'antd';
import { 
    DeleteOutlined, 
    InfoCircleOutlined, 
    ReloadOutlined,
    CheckCircleOutlined,
    ExclamationCircleOutlined,
    ClockCircleOutlined
} from '@ant-design/icons';
import './DXTExtensionList.css';

// {{CHENGQI:
// Action: Added
// Timestamp: 2025-01-17 09:50:00 +08:00
// Reason: 创建DXT扩展列表管理组件，提供扩展的查看、启用/禁用、卸载等功能
// Principle_Applied: KISS - 保持界面简洁明了，单一职责 - 专注扩展管理
// Optimization: 使用React hooks优化性能，提供友好的用户交互体验
// Architectural_Note (AR): 遵循React组件化设计，与上传组件配合使用
// Documentation_Note (DW): 提供清晰的扩展状态显示和操作反馈
// }}

const { Title, Text, Paragraph } = Typography;

const DXTExtensionList = ({ onExtensionChange, refreshTrigger }) => {
    const [extensions, setExtensions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [actionLoading, setActionLoading] = useState({});
    const [detailModalVisible, setDetailModalVisible] = useState(false);
    const [selectedExtension, setSelectedExtension] = useState(null);

    // 获取扩展列表
    const fetchExtensions = useCallback(async () => {
        setLoading(true);
        try {
            const response = await fetch('/api/dxt/list');
            const result = await response.json();
            
            if (result.success) {
                setExtensions(result.data || []);
            } else {
                message.error(`获取扩展列表失败: ${result.message}`);
                setExtensions([]);
            }
        } catch (error) {
            console.error('获取扩展列表错误:', error);
            message.error('获取扩展列表失败，请检查网络连接');
            setExtensions([]);
        } finally {
            setLoading(false);
        }
    }, []);

    // 切换扩展启用状态
    const toggleExtension = useCallback(async (extensionName, enabled) => {
        setActionLoading(prev => ({ ...prev, [extensionName]: true }));
        
        try {
            const response = await fetch('/api/dxt/toggle', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    extension_name: extensionName,
                    enabled: enabled
                }),
            });

            const result = await response.json();
            
            if (result.success) {
                message.success(enabled ? `扩展 "${extensionName}" 已启用` : `扩展 "${extensionName}" 已禁用`);
                // 更新本地状态
                setExtensions(prev => 
                    prev.map(ext => 
                        ext.name === extensionName 
                            ? { ...ext, enabled: enabled }
                            : ext
                    )
                );
                
                // 通知父组件
                if (onExtensionChange) {
                    onExtensionChange();
                }
            } else {
                message.error(`操作失败: ${result.message}`);
                // 如果失败，恢复开关状态
                setExtensions(prev => 
                    prev.map(ext => 
                        ext.name === extensionName 
                            ? { ...ext, enabled: !enabled }
                            : ext
                    )
                );
            }
        } catch (error) {
            console.error('切换扩展状态错误:', error);
            message.error('操作失败，请检查网络连接');
            // 恢复开关状态
            setExtensions(prev => 
                prev.map(ext => 
                    ext.name === extensionName 
                        ? { ...ext, enabled: !enabled }
                        : ext
                )
            );
        } finally {
            setActionLoading(prev => ({ ...prev, [extensionName]: false }));
        }
    }, [onExtensionChange]);

    // 卸载扩展
    const uninstallExtension = useCallback(async (extensionName) => {
        setActionLoading(prev => ({ ...prev, [`${extensionName}_uninstall`]: true }));
        
        try {
            const response = await fetch(`/api/dxt/uninstall/${extensionName}`, {
                method: 'DELETE',
            });

            const result = await response.json();
            
            if (result.success) {
                message.success(`扩展 "${extensionName}" 已成功卸载`);
                // 从列表中移除
                setExtensions(prev => prev.filter(ext => ext.name !== extensionName));
                
                // 通知父组件
                if (onExtensionChange) {
                    onExtensionChange();
                }
            } else {
                message.error(`卸载失败: ${result.message}`);
            }
        } catch (error) {
            console.error('卸载扩展错误:', error);
            message.error('卸载失败，请检查网络连接');
        } finally {
            setActionLoading(prev => ({ ...prev, [`${extensionName}_uninstall`]: false }));
        }
    }, [onExtensionChange]);

    // 查看扩展详情
    const showExtensionDetail = useCallback(async (extension) => {
        try {
            const response = await fetch(`/api/dxt/info/${extension.name}`);
            const result = await response.json();
            
            if (result.success) {
                setSelectedExtension(result.data);
                setDetailModalVisible(true);
            } else {
                message.error(`获取扩展详情失败: ${result.message}`);
            }
        } catch (error) {
            console.error('获取扩展详情错误:', error);
            message.error('获取扩展详情失败，请检查网络连接');
        }
    }, []);

    // 格式化文件大小
    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // 格式化时间
    const formatTime = (dateString) => {
        if (!dateString) return '未知';
        try {
            return new Date(dateString).toLocaleString('zh-CN');
        } catch {
            return '未知';
        }
    };

    // 获取状态标签
    const getStatusTag = (extension) => {
        if (extension.enabled) {
            return <Tag color="green" icon={<CheckCircleOutlined />}>已启用</Tag>;
        } else {
            return <Tag color="orange" icon={<ExclamationCircleOutlined />}>已禁用</Tag>;
        }
    };

    // 组件挂载和刷新触发器变化时获取列表
    useEffect(() => {
        fetchExtensions();
    }, [fetchExtensions, refreshTrigger]);

    return (
        <Card 
            title="已安装扩展" 
            className="dxt-extension-list"
            extra={
                <Button 
                    icon={<ReloadOutlined />} 
                    onClick={fetchExtensions}
                    loading={loading}
                    size="small"
                >
                    刷新
                </Button>
            }
        >
            {extensions.length === 0 ? (
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="暂无已安装的扩展"
                    style={{ padding: '40px 0' }}
                >
                    <Text type="secondary">
                        您可以通过上传DXT文件来安装新的扩展
                    </Text>
                </Empty>
            ) : (
                <List
                    dataSource={extensions}
                    loading={loading}
                    renderItem={(extension) => (
                        <List.Item
                            key={extension.name}
                            className="extension-item"
                            actions={[
                                <Tooltip title={extension.enabled ? '禁用扩展' : '启用扩展'}>
                                    <Switch
                                        checked={extension.enabled}
                                        loading={actionLoading[extension.name]}
                                        onChange={(checked) => toggleExtension(extension.name, checked)}
                                        size="small"
                                    />
                                </Tooltip>,
                                <Tooltip title="查看详情">
                                    <Button
                                        type="text"
                                        icon={<InfoCircleOutlined />}
                                        onClick={() => showExtensionDetail(extension)}
                                        size="small"
                                    />
                                </Tooltip>,
                                <Popconfirm
                                    title="确认卸载"
                                    description={`确定要卸载扩展 "${extension.name}" 吗？此操作不可恢复。`}
                                    onConfirm={() => uninstallExtension(extension.name)}
                                    okText="确认"
                                    cancelText="取消"
                                    okType="danger"
                                >
                                    <Tooltip title="卸载扩展">
                                        <Button
                                            type="text"
                                            danger
                                            icon={<DeleteOutlined />}
                                            loading={actionLoading[`${extension.name}_uninstall`]}
                                            size="small"
                                        />
                                    </Tooltip>
                                </Popconfirm>
                            ]}
                        >
                            <List.Item.Meta
                                title={
                                    <Space>
                                        <Text strong>{extension.name}</Text>
                                        {getStatusTag(extension)}
                                        <Tag color="blue">v{extension.version}</Tag>
                                    </Space>
                                }
                                description={
                                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                                        <Text type="secondary">
                                            {extension.description || '暂无描述'}
                                        </Text>
                                        <Space size="small">
                                            <Text type="secondary">
                                                <ClockCircleOutlined /> 安装时间: {formatTime(extension.installed_at)}
                                            </Text>
                                            {extension.file_size > 0 && (
                                                <Text type="secondary">
                                                    大小: {formatFileSize(extension.file_size)}
                                                </Text>
                                            )}
                                            {extension.author && (
                                                <Text type="secondary">
                                                    作者: {extension.author}
                                                </Text>
                                            )}
                                        </Space>
                                    </Space>
                                }
                            />
                        </List.Item>
                    )}
                />
            )}

            {/* 扩展详情弹窗 */}
            <Modal
                title="扩展详情"
                open={detailModalVisible}
                onCancel={() => setDetailModalVisible(false)}
                footer={[
                    <Button key="close" onClick={() => setDetailModalVisible(false)}>
                        关闭
                    </Button>
                ]}
                width={600}
            >
                {selectedExtension && (
                    <Space direction="vertical" size="large" style={{ width: '100%' }}>
                        <Descriptions column={1} bordered size="small">
                            <Descriptions.Item label="扩展名称">
                                {selectedExtension.name}
                            </Descriptions.Item>
                            <Descriptions.Item label="版本号">
                                {selectedExtension.version}
                            </Descriptions.Item>
                            <Descriptions.Item label="状态">
                                {getStatusTag(selectedExtension)}
                            </Descriptions.Item>
                            <Descriptions.Item label="作者">
                                {selectedExtension.author || '未知'}
                            </Descriptions.Item>
                            <Descriptions.Item label="安装时间">
                                {formatTime(selectedExtension.installed_at)}
                            </Descriptions.Item>
                            <Descriptions.Item label="文件大小">
                                {formatFileSize(selectedExtension.file_size)}
                            </Descriptions.Item>
                            <Descriptions.Item label="安装路径">
                                <Text code copyable={{ text: selectedExtension.install_path }}>
                                    {selectedExtension.install_path}
                                </Text>
                            </Descriptions.Item>
                            {selectedExtension.file_hash && (
                                <Descriptions.Item label="文件哈希">
                                    <Text code copyable={{ text: selectedExtension.file_hash }}>
                                        {selectedExtension.file_hash.substring(0, 16)}...
                                    </Text>
                                </Descriptions.Item>
                            )}
                        </Descriptions>

                        {selectedExtension.description && (
                            <div>
                                <Title level={5}>扩展描述</Title>
                                <Paragraph>
                                    {selectedExtension.description}
                                </Paragraph>
                            </div>
                        )}

                        <Alert
                            type="info"
                            showIcon
                            message="提示"
                            description="扩展的启用/禁用状态会影响MCP配置。禁用扩展后，相关功能将不可用。"
                        />
                    </Space>
                )}
            </Modal>
        </Card>
    );
};

export default DXTExtensionList; 