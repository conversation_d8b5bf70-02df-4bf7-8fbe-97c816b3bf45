import React, { useState, useCallback } from 'react';
import { Upload, Button, message, Progress, Card, Typography, Space, Tag, Alert, Modal } from 'antd';
import { InboxOutlined, UploadOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import './DXTUpload.css';

// {{CHENGQI:
// Action: Added
// Timestamp: 2025-01-17 09:45:00 +08:00
// Reason: 创建DXT扩展文件上传组件，支持拖拽上传和文件管理功能
// Principle_Applied: KISS - 保持组件简洁易用，单一职责 - 专注文件上传处理
// Optimization: 使用React hooks优化性能，Ant Design提供一致的UI体验
// Architectural_Note (AR): 遵循React组件化设计，便于复用和维护
// Documentation_Note (DW): 提供清晰的用户交互提示和错误反馈
// }}

const { Dragger } = Upload;
const { Title, Text } = Typography;

const DXTUpload = ({ onUploadSuccess, onUploadError, disabled = false }) => {
    const [uploading, setUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [selectedFile, setSelectedFile] = useState(null);
    const [validationResult, setValidationResult] = useState(null);
    const [overwriteModalVisible, setOverwriteModalVisible] = useState(false);
    const [pendingFile, setPendingFile] = useState(null);
    const [existingExtensionName, setExistingExtensionName] = useState('');

    // 文件验证函数
    const validateFile = useCallback(async (file) => {
        // 前端基本验证
        if (!file.name.toLowerCase().endsWith('.dxt')) {
            message.error('只支持.dxt格式的扩展文件');
            return false;
        }

        // 文件大小检查 (50MB)
        const MAX_SIZE = 50 * 1024 * 1024;
        if (file.size > MAX_SIZE) {
            message.error('文件大小不能超过50MB');
            return false;
        }

        // 调用后端验证API
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/dxt/validate', {
                method: 'POST',
                body: formData,
            });

            const result = await response.json();
            
            if (result.success) {
                setValidationResult({
                    valid: true,
                    manifest: result.data
                });
                return true;
            } else {
                setValidationResult({
                    valid: false,
                    error: result.message
                });
                message.error(`文件验证失败: ${result.message}`);
                return false;
            }
        } catch (error) {
            console.error('文件验证错误:', error);
            message.error('文件验证失败，请检查网络连接');
            return false;
        }
    }, []);

    // 文件上传处理
    const handleUpload = useCallback(async (options) => {
        const { file, onProgress, onSuccess, onError } = options;
        
        setUploading(true);
        setUploadProgress(0);
        setSelectedFile(file);
        setValidationResult(null);

        try {
            // 先验证文件
            const isValid = await validateFile(file);
            if (!isValid) {
                setUploading(false);
                onError(new Error('文件验证失败'));
                return;
            }

            // 检查扩展是否已存在
            if (validationResult && validationResult.valid && validationResult.manifest) {
                const extensionName = validationResult.manifest.name;
                try {
                    const checkResponse = await fetch(`/api/dxt/check/${extensionName}`);
                    const checkResult = await checkResponse.json();

                    if (checkResult.success && checkResult.data.exists) {
                        // 扩展已存在，显示覆盖确认对话框
                        setUploading(false);
                        setPendingFile(file);
                        setExistingExtensionName(extensionName);
                        setOverwriteModalVisible(true);
                        return;
                    }
                } catch (error) {
                    console.warn('检查扩展是否存在失败，继续安装:', error);
                }
            }

            // 执行实际上传
            await performUpload(file, false, onProgress, onSuccess, onError);

        } catch (error) {
            setUploading(false);
            setUploadProgress(0);
            console.error('上传错误:', error);
            const errorMsg = `上传失败: ${error.message}`;
            message.error(errorMsg);
            onError(error);
            if (onUploadError) {
                onUploadError(errorMsg);
            }
        }
    }, [validateFile, validationResult, onUploadSuccess, onUploadError]);

    // 执行实际上传的函数
    const performUpload = useCallback(async (file, forceUpdate, onProgress, onSuccess, onError) => {
        try {
            // 创建FormData
            const formData = new FormData();
            formData.append('file', file);
            formData.append('force_update', forceUpdate.toString());

            // 上传文件
            const xhr = new XMLHttpRequest();
            
            // 监听上传进度
            xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable) {
                    const percent = Math.round((event.loaded * 100) / event.total);
                    setUploadProgress(percent);
                    onProgress({ percent });
                }
            });

            // 处理上传完成
            xhr.addEventListener('load', () => {
                setUploading(false);
                setUploadProgress(0);
                
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        message.success(`扩展 "${response.data.name}" 安装成功！`);
                        onSuccess(response);
                        if (onUploadSuccess) {
                            onUploadSuccess(response.data);
                        }
                        // 清空选择的文件和验证结果
                        setSelectedFile(null);
                        setValidationResult(null);
                    } else {
                        message.error(`安装失败: ${response.message}`);
                        onError(new Error(response.message));
                        if (onUploadError) {
                            onUploadError(response.message);
                        }
                    }
                } else {
                    const errorMsg = '上传失败，请稍后重试';
                    message.error(errorMsg);
                    onError(new Error(errorMsg));
                    if (onUploadError) {
                        onUploadError(errorMsg);
                    }
                }
            });

            // 处理上传错误
            xhr.addEventListener('error', () => {
                setUploading(false);
                setUploadProgress(0);
                const errorMsg = '上传失败，请检查网络连接';
                message.error(errorMsg);
                onError(new Error(errorMsg));
                if (onUploadError) {
                    onUploadError(errorMsg);
                }
            });

            // 开始上传
            xhr.open('POST', '/api/dxt/upload');
            xhr.send(formData);

        } catch (error) {
            setUploading(false);
            setUploadProgress(0);
            console.error('上传错误:', error);
            const errorMsg = `上传失败: ${error.message}`;
            message.error(errorMsg);
            onError(error);
            if (onUploadError) {
                onUploadError(errorMsg);
            }
        }
    }, [onUploadSuccess, onUploadError]);

    // 文件选择前的检查
    const beforeUpload = useCallback((file) => {
        // 基本格式检查
        if (!file.name.toLowerCase().endsWith('.dxt')) {
            message.error('只支持.dxt格式的扩展文件');
            return false;
        }

        // 文件大小检查
        const MAX_SIZE = 50 * 1024 * 1024;
        if (file.size > MAX_SIZE) {
            message.error('文件大小不能超过50MB');
            return false;
        }

        return true;
    }, []);

    // 确认覆盖安装
    const handleOverwriteConfirm = useCallback(async () => {
        if (!pendingFile) return;

        setOverwriteModalVisible(false);
        setUploading(true);
        setUploadProgress(0);

        try {
            await performUpload(pendingFile, true,
                ({ percent }) => setUploadProgress(percent),
                (response) => {
                    message.success(`扩展 "${response.data.name}" 覆盖安装成功！`);
                    if (onUploadSuccess) {
                        onUploadSuccess(response.data);
                    }
                    // 清空状态
                    setSelectedFile(null);
                    setValidationResult(null);
                    setPendingFile(null);
                    setExistingExtensionName('');
                },
                (error) => {
                    const errorMsg = `覆盖安装失败: ${error.message}`;
                    message.error(errorMsg);
                    if (onUploadError) {
                        onUploadError(errorMsg);
                    }
                }
            );
        } catch (error) {
            setUploading(false);
            console.error('覆盖安装错误:', error);
            message.error(`覆盖安装失败: ${error.message}`);
        }
    }, [pendingFile, performUpload, onUploadSuccess, onUploadError]);

    // 取消覆盖安装
    const handleOverwriteCancel = useCallback(() => {
        setOverwriteModalVisible(false);
        setPendingFile(null);
        setExistingExtensionName('');
        setUploading(false);
        setUploadProgress(0);
    }, []);

    // 渲染验证结果
    const renderValidationResult = () => {
        if (!validationResult) return null;

        if (validationResult.valid && validationResult.manifest) {
            const { manifest } = validationResult;
            return (
                <Alert
                    type="success"
                    showIcon
                    icon={<CheckCircleOutlined />}
                    message="文件验证通过"
                    description={
                        <Space direction="vertical" size="small">
                            <Text><strong>名称:</strong> {manifest.name}</Text>
                            <Text><strong>版本:</strong> {manifest.version}</Text>
                            <Text><strong>描述:</strong> {manifest.description}</Text>
                            <Text><strong>作者:</strong> {
                                typeof manifest.author === 'string'
                                    ? manifest.author
                                    : (manifest.author?.name || '未知')
                            }</Text>
                        </Space>
                    }
                    style={{ marginTop: 16 }}
                />
            );
        } else {
            return (
                <Alert
                    type="error"
                    showIcon
                    icon={<ExclamationCircleOutlined />}
                    message="文件验证失败"
                    description={validationResult.error}
                    style={{ marginTop: 16 }}
                />
            );
        }
    };

    const uploadProps = {
        name: 'file',
        multiple: false,
        accept: '.dxt',
        disabled: disabled || uploading,
        customRequest: handleUpload,
        beforeUpload: beforeUpload,
        showUploadList: false, // 我们自定义显示逻辑
        onChange: (info) => {
            // 可以在这里处理文件状态变化
        },
        onDrop: (e) => {
            console.log('拖拽文件:', e.dataTransfer.files);
        },
    };

    return (
        <Card title="DXT扩展安装" className="dxt-upload-card">
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                {/* 文件上传区域 */}
                <Dragger {...uploadProps} className="dxt-upload-dragger">
                    <p className="ant-upload-drag-icon">
                        <InboxOutlined style={{ color: uploading ? '#ccc' : '#1890ff' }} />
                    </p>
                    <p className="ant-upload-text">
                        {uploading ? '正在上传...' : '点击或拖拽DXT文件到此区域上传'}
                    </p>
                    <p className="ant-upload-hint">
                        支持.dxt格式的扩展文件，文件大小不超过50MB
                        <br />
                        <Tag color="blue">安全验证</Tag>
                        <Tag color="green">自动安装</Tag>
                        <Tag color="orange">MCP集成</Tag>
                    </p>
                </Dragger>

                {/* 上传进度 */}
                {uploading && (
                    <div className="upload-progress">
                        <Text>上传进度:</Text>
                        <Progress 
                            percent={uploadProgress} 
                            status={uploadProgress === 100 ? 'success' : 'active'}
                            showInfo
                        />
                        {selectedFile && (
                            <Text type="secondary">
                                正在上传: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                            </Text>
                        )}
                    </div>
                )}

                {/* 验证结果展示 */}
                {renderValidationResult()}

                {/* 手动选择文件按钮 */}
                <div style={{ textAlign: 'center' }}>
                    <Upload {...uploadProps}>
                        <Button 
                            type="primary" 
                            icon={<UploadOutlined />} 
                            disabled={disabled || uploading}
                            loading={uploading}
                        >
                            {uploading ? '上传中...' : '选择DXT文件'}
                        </Button>
                    </Upload>
                </div>

                {/* 使用说明 */}
                <Alert
                    type="info"
                    showIcon
                    message="使用说明"
                    description={
                        <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
                            <li>DXT扩展是包含manifest.json的ZIP格式文件</li>
                            <li>上传的扩展将自动解压并安装到系统</li>
                            <li>安装后的扩展会自动添加到MCP配置中</li>
                            <li>您可以随时在扩展列表中启用或禁用扩展</li>
                        </ul>
                    }
                />

                {/* 覆盖确认模态框 */}
                <Modal
                    title="扩展已存在"
                    open={overwriteModalVisible}
                    onOk={handleOverwriteConfirm}
                    onCancel={handleOverwriteCancel}
                    okText="覆盖安装"
                    cancelText="取消"
                    okButtonProps={{ danger: true }}
                >
                    <p>
                        扩展 <strong>"{existingExtensionName}"</strong> 已经存在。
                    </p>
                    <p>
                        是否要覆盖安装？这将替换现有的扩展文件和配置。
                    </p>
                </Modal>
            </Space>
        </Card>
    );
};

export default DXTUpload; 