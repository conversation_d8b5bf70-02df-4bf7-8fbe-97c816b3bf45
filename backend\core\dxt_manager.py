"""
DXT (Desktop Extensions) 扩展管理模块
负责dxt文件的验证、解压、安装和配置管理
"""

import os
import json
import zipfile
import hashlib
import tempfile
import shutil
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime

logger = logging.getLogger(__name__)

class DXTManager:
    """DXT扩展管理器"""
    
    # 支持的dxt文件最大大小 (50MB)
    MAX_DXT_SIZE = 50 * 1024 * 1024
    
    # 必需的manifest字段
    REQUIRED_MANIFEST_FIELDS = ["name", "version", "main", "description"]
    
    def __init__(self, config_path: str, install_base_dir: str):
        """
        初始化DXT管理器
        
        Args:
            config_path: MCP配置文件路径
            install_base_dir: 扩展安装基础目录
        """
        self.config_path = Path(config_path)
        self.install_base_dir = Path(install_base_dir)
        
        # 确保安装目录存在
        self.install_base_dir.mkdir(parents=True, exist_ok=True)
        
        # 扩展安装目录
        self.extensions_dir = self.install_base_dir / "extensions"
        self.extensions_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"DXT管理器初始化完成 - 配置: {self.config_path}, 安装目录: {self.install_base_dir}")

    def _safe_remove_file(self, file_path: Path, max_retries: int = 3, delay: float = 0.1) -> bool:
        """
        安全删除文件，处理Windows文件锁定问题

        Args:
            file_path: 要删除的文件路径
            max_retries: 最大重试次数
            delay: 重试间隔（秒）

        Returns:
            是否成功删除
        """
        for attempt in range(max_retries):
            try:
                if file_path.exists():
                    file_path.unlink()
                return True
            except (PermissionError, OSError) as e:
                if attempt < max_retries - 1:
                    logger.debug(f"删除文件失败，第{attempt + 1}次重试: {e}")
                    time.sleep(delay)
                else:
                    logger.warning(f"删除文件失败，已达最大重试次数: {e}")
                    return False
        return False

    def validate_dxt_file(self, file_path: Path) -> Tuple[bool, str]:
        """
        验证dxt文件的完整性和格式
        
        Args:
            file_path: dxt文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 检查文件存在
            if not file_path.exists():
                return False, "文件不存在"
            
            # 检查文件大小
            if file_path.stat().st_size > self.MAX_DXT_SIZE:
                return False, f"文件大小超过限制 ({self.MAX_DXT_SIZE // 1024 // 1024}MB)"
            
            # 检查文件扩展名
            if file_path.suffix.lower() != '.dxt':
                return False, "文件扩展名必须为.dxt"

            # 检查ZIP文件内容（直接尝试打开，避免 zipfile.is_zipfile 的文件句柄问题）
            try:
                with zipfile.ZipFile(file_path, 'r') as zip_file:
                    # 检查是否包含manifest.json
                    if 'manifest.json' not in zip_file.namelist():
                        return False, "缺少manifest.json文件"

                    # 检查危险路径
                    for name in zip_file.namelist():
                        if self._is_dangerous_path(name):
                            return False, f"包含危险路径: {name}"

                    # 验证manifest.json格式
                    try:
                        manifest_content = zip_file.read('manifest.json')
                        manifest = json.loads(manifest_content.decode('utf-8'))

                        # 检查必需字段
                        for field in self.REQUIRED_MANIFEST_FIELDS:
                            if field not in manifest:
                                return False, f"manifest.json缺少必需字段: {field}"

                        # 验证名称格式
                        if not self._is_valid_extension_name(manifest['name']):
                            return False, "扩展名称格式无效(只允许字母、数字、短划线、下划线)"

                    except json.JSONDecodeError as e:
                        return False, f"manifest.json格式错误: {str(e)}"
                    except UnicodeDecodeError:
                        return False, "manifest.json编码错误，必须为UTF-8"
            except zipfile.BadZipFile:
                return False, "不是有效的dxt文件格式(ZIP)"
            except Exception as e:
                logger.error(f"读取ZIP文件时出错: {e}")
                return False, f"文件读取失败: {str(e)}"
            
            return True, "验证通过"
            
        except Exception as e:
            logger.error(f"验证dxt文件时出错: {e}")
            return False, f"验证失败: {str(e)}"
    
    def extract_manifest(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        提取dxt文件的manifest配置
        
        Args:
            file_path: dxt文件路径
            
        Returns:
            manifest配置字典，失败返回None
        """
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                manifest_content = zip_file.read('manifest.json')
                manifest = json.loads(manifest_content.decode('utf-8'))
                
                # 添加文件元信息
                manifest['_meta'] = {
                    'file_path': str(file_path),
                    'file_size': file_path.stat().st_size,
                    'extracted_at': datetime.now().isoformat(),
                    'file_hash': self._calculate_file_hash(file_path)
                }
                
                return manifest
                
        except Exception as e:
            logger.error(f"提取manifest失败: {e}")
            return None
    
    def install_extension(self, file_path: Path, force_update: bool = False) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        安装dxt扩展
        
        Args:
            file_path: dxt文件路径
            force_update: 是否强制更新已存在的扩展
            
        Returns:
            (是否成功, 消息, 扩展信息)
        """
        try:
            # 验证文件
            is_valid, error_msg = self.validate_dxt_file(file_path)
            if not is_valid:
                return False, f"文件验证失败: {error_msg}", None
            
            # 提取manifest
            manifest = self.extract_manifest(file_path)
            if not manifest:
                return False, "无法读取扩展配置", None
            
            extension_name = manifest['name']
            extension_dir = self.extensions_dir / extension_name
            
            # 检查是否已安装
            if extension_dir.exists() and not force_update:
                return False, f"扩展 {extension_name} 已存在，使用force_update=True强制更新", None
            
            # 创建临时目录进行解压
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # 解压到临时目录
                with zipfile.ZipFile(file_path, 'r') as zip_file:
                    zip_file.extractall(temp_path)
                
                # 如果目标目录存在，先删除
                if extension_dir.exists():
                    shutil.rmtree(extension_dir)
                
                # 移动到最终位置
                shutil.move(str(temp_path), str(extension_dir))
                
                logger.info(f"扩展 {extension_name} 安装成功: {extension_dir}")
            
            # 更新MCP配置
            mcp_config = self._generate_mcp_config(manifest, extension_dir)
            if mcp_config:
                success = self._update_mcp_configuration(extension_name, mcp_config)
                if not success:
                    logger.warning(f"扩展 {extension_name} 安装成功，但MCP配置更新失败")
            
            # 保存扩展信息
            extension_info = {
                'name': extension_name,
                'version': manifest.get('version', '未知'),
                'description': manifest.get('description', ''),
                'author': manifest.get('author', '未知'),
                'installed_at': datetime.now().isoformat(),
                'install_path': str(extension_dir),
                'manifest': manifest,
                'enabled': True
            }
            
            self._save_extension_info(extension_name, extension_info)
            
            return True, f"扩展 {extension_name} v{manifest.get('version', '未知')} 安装成功", extension_info
            
        except Exception as e:
            logger.error(f"安装扩展失败: {e}")
            return False, f"安装失败: {str(e)}", None
    
    def uninstall_extension(self, extension_name: str) -> Tuple[bool, str]:
        """
        卸载扩展
        
        Args:
            extension_name: 扩展名称
            
        Returns:
            (是否成功, 消息)
        """
        try:
            extension_dir = self.extensions_dir / extension_name
            
            if not extension_dir.exists():
                return False, f"扩展 {extension_name} 不存在"
            
            # 从MCP配置中移除
            self._remove_from_mcp_configuration(extension_name)
            
            # 删除扩展目录
            shutil.rmtree(extension_dir)
            
            # 删除扩展信息
            self._remove_extension_info(extension_name)
            
            logger.info(f"扩展 {extension_name} 卸载成功")
            return True, f"扩展 {extension_name} 已卸载"
            
        except Exception as e:
            logger.error(f"卸载扩展失败: {e}")
            return False, f"卸载失败: {str(e)}"
    
    def list_installed_extensions(self) -> List[Dict[str, Any]]:
        """
        获取已安装扩展列表
        
        Returns:
            扩展信息列表
        """
        extensions = []
        
        try:
            if not self.extensions_dir.exists():
                return extensions
            
            for ext_dir in self.extensions_dir.iterdir():
                if ext_dir.is_dir():
                    extension_info = self._load_extension_info(ext_dir.name)
                    if extension_info:
                        extensions.append(extension_info)
                    else:
                        # 尝试从manifest重建信息
                        manifest_path = ext_dir / "manifest.json"
                        if manifest_path.exists():
                            try:
                                with open(manifest_path, 'r', encoding='utf-8') as f:
                                    manifest = json.load(f)
                                
                                extension_info = {
                                    'name': ext_dir.name,
                                    'version': manifest.get('version', '未知'),
                                    'description': manifest.get('description', ''),
                                    'author': manifest.get('author', '未知'),
                                    'installed_at': '未知',
                                    'install_path': str(ext_dir),
                                    'manifest': manifest,
                                    'enabled': True,
                                    'rebuilt': True  # 标记为重建的信息
                                }
                                extensions.append(extension_info)
                                
                            except Exception as e:
                                logger.warning(f"无法读取扩展 {ext_dir.name} 的配置: {e}")
        
        except Exception as e:
            logger.error(f"获取扩展列表失败: {e}")
        
        return extensions
    
    def enable_extension(self, extension_name: str, enabled: bool = True) -> Tuple[bool, str]:
        """
        启用或禁用扩展
        
        Args:
            extension_name: 扩展名称
            enabled: 是否启用
            
        Returns:
            (是否成功, 消息)
        """
        try:
            extension_info = self._load_extension_info(extension_name)
            if not extension_info:
                return False, f"扩展 {extension_name} 不存在"
            
            # 更新扩展信息
            extension_info['enabled'] = enabled
            self._save_extension_info(extension_name, extension_info)
            
            # 更新MCP配置
            if enabled:
                extension_dir = Path(extension_info['install_path'])
                mcp_config = self._generate_mcp_config(extension_info['manifest'], extension_dir)
                if mcp_config:
                    self._update_mcp_configuration(extension_name, mcp_config)
            else:
                self._remove_from_mcp_configuration(extension_name)
            
            action = "启用" if enabled else "禁用"
            return True, f"扩展 {extension_name} 已{action}"
            
        except Exception as e:
            logger.error(f"切换扩展状态失败: {e}")
            return False, f"操作失败: {str(e)}"
    
    def _is_dangerous_path(self, path: str) -> bool:
        """检查路径是否包含危险字符"""
        # 防护路径遍历攻击
        if '..' in path or path.startswith('/') or ':' in path:
            return True
        
        # 检查Windows保留名称
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL'] + [f'COM{i}' for i in range(1, 10)] + [f'LPT{i}' for i in range(1, 10)]
        path_parts = Path(path).parts
        for part in path_parts:
            if part.upper() in reserved_names:
                return True
        
        return False
    
    def _is_valid_extension_name(self, name: str) -> bool:
        """验证扩展名称格式"""
        import re
        # 只允许字母、数字、短划线、下划线，长度3-50
        pattern = r'^[a-zA-Z0-9_-]{3,50}$'
        return bool(re.match(pattern, name))
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _generate_mcp_config(self, manifest: Dict[str, Any], extension_dir: Path) -> Optional[Dict[str, Any]]:
        """
        根据manifest生成MCP服务器配置
        
        Args:
            manifest: 扩展manifest
            extension_dir: 扩展安装目录
            
        Returns:
            MCP配置字典，失败返回None
        """
        try:
            # 检查是否包含MCP配置
            mcp_config = manifest.get('mcp', {})
            if not mcp_config:
                return None
            
            # 构建基础配置
            config = {
                'enabled': True,
                'description': f"DXT扩展: {manifest.get('description', '')}",
                'isActive': False
            }
            
            # 处理不同类型的MCP服务器
            if 'command' in mcp_config:
                # stdio类型
                main_file = extension_dir / manifest['main']
                config.update({
                    'command': mcp_config['command'],
                    'args': mcp_config.get('args', []),
                    'env': mcp_config.get('env', {})
                })
                
                # 替换路径占位符
                if '__EXTENSION_PATH__' in str(config['command']):
                    config['command'] = str(config['command']).replace('__EXTENSION_PATH__', str(extension_dir))
                
                config['args'] = [
                    str(arg).replace('__EXTENSION_PATH__', str(extension_dir)) 
                    for arg in config['args']
                ]
                
            elif 'url' in mcp_config:
                # SSE类型
                config['url'] = mcp_config['url']
            
            return config
            
        except Exception as e:
            logger.error(f"生成MCP配置失败: {e}")
            return None
    
    def _update_mcp_configuration(self, extension_name: str, mcp_config: Dict[str, Any]) -> bool:
        """更新MCP配置文件"""
        try:
            # 读取现有配置
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {'mcpServers': {}}
            
            # 确保mcpServers存在
            if 'mcpServers' not in config:
                config['mcpServers'] = {}
            
            # 添加或更新扩展配置
            config['mcpServers'][extension_name] = mcp_config
            
            # 写回配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"MCP配置已更新: {extension_name}")
            return True
            
        except Exception as e:
            logger.error(f"更新MCP配置失败: {e}")
            return False
    
    def _remove_from_mcp_configuration(self, extension_name: str) -> bool:
        """从MCP配置中移除扩展"""
        try:
            if not self.config_path.exists():
                return True
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if 'mcpServers' in config and extension_name in config['mcpServers']:
                del config['mcpServers'][extension_name]
                
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                logger.info(f"已从MCP配置移除: {extension_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"从MCP配置移除扩展失败: {e}")
            return False
    
    def _get_extension_info_path(self, extension_name: str) -> Path:
        """获取扩展信息文件路径"""
        return self.extensions_dir / extension_name / ".extension_info.json"
    
    def _save_extension_info(self, extension_name: str, info: Dict[str, Any]) -> bool:
        """保存扩展信息"""
        try:
            info_path = self._get_extension_info_path(extension_name)
            info_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(info, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            logger.error(f"保存扩展信息失败: {e}")
            return False
    
    def _load_extension_info(self, extension_name: str) -> Optional[Dict[str, Any]]:
        """加载扩展信息"""
        try:
            info_path = self._get_extension_info_path(extension_name)
            if info_path.exists():
                with open(info_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
            
        except Exception as e:
            logger.error(f"加载扩展信息失败: {e}")
            return None
    
    def _remove_extension_info(self, extension_name: str) -> bool:
        """删除扩展信息"""
        try:
            info_path = self._get_extension_info_path(extension_name)
            if info_path.exists():
                info_path.unlink()
            return True
            
        except Exception as e:
            logger.error(f"删除扩展信息失败: {e}")
            return False 